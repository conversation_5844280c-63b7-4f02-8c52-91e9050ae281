import Vapi from "@vapi-ai/web"

// Initialize VAPI with error handling
let vapi: Vapi;

try {
  if (!process.env.NEXT_PUBLIC_VAPI_PUBLIC_KEY) {
    console.error('VAPI Public Key is missing! Please check your environment variables.');
    // Create a mock VAPI instance to prevent app crashes
    vapi = new Vapi('placeholder-key');
  } else {
    vapi = new Vapi(process.env.NEXT_PUBLIC_VAPI_PUBLIC_KEY);

    // Add global error handler
    vapi.on('error', (error: Error) => {
      console.error('Global VAPI error:', error);
    });

    // Add global call-end handler
    vapi.on('call-end', () => {
      console.log('Global call-end event');
    });
  }
} catch (error) {
  console.error('Failed to initialize VAPI:', error);
  // Create a mock VAPI instance to prevent app crashes
  vapi = new Vapi('placeholder-key');
}

export { vapi };