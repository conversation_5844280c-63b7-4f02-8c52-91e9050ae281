import { vapi } from './vapi.sdk';

export interface VapiConnectionOptions {
  userName?: string;
  userId?: string;
  maxRetries?: number;
  retryDelay?: number;
}

export class VapiConnectionManager {
  private retryCount = 0;
  private maxRetries: number;
  private retryDelay: number;
  private isConnecting = false;

  constructor(options: VapiConnectionOptions = {}) {
    this.maxRetries = options.maxRetries || 3;
    this.retryDelay = options.retryDelay || 2000;
  }

  async startCall(options: VapiConnectionOptions): Promise<boolean> {
    if (this.isConnecting) {
      console.warn('Connection already in progress');
      return false;
    }

    this.isConnecting = true;
    this.retryCount = 0;

    try {
      // Check browser capabilities first
      await this.checkBrowserCapabilities();
      
      // Request microphone permission
      await this.requestMicrophonePermission();
      
      // Start the VAPI call
      await this.attemptConnection(options);
      
      this.isConnecting = false;
      return true;
    } catch (error) {
      this.isConnecting = false;
      console.error('Failed to start call after all retries:', error);
      throw error;
    }
  }

  private async checkBrowserCapabilities(): Promise<void> {
    // Check for required browser features
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      throw new Error('Your browser does not support microphone access. Please use a modern browser.');
    }

    if (!window.RTCPeerConnection) {
      throw new Error('Your browser does not support WebRTC. Please use a modern browser.');
    }

    if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
      throw new Error('Microphone access requires HTTPS. Please use a secure connection.');
    }
  }

  private async requestMicrophonePermission(): Promise<void> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      // Stop the stream immediately as we just needed permission
      stream.getTracks().forEach(track => track.stop());
      console.log('Microphone permission granted');
    } catch (error) {
      console.error('Microphone permission denied:', error);
      throw new Error('Microphone access is required for the interview. Please allow microphone access and try again.');
    }
  }

  private async attemptConnection(options: VapiConnectionOptions): Promise<void> {
    while (this.retryCount <= this.maxRetries) {
      try {
        console.log(`Connection attempt ${this.retryCount + 1}/${this.maxRetries + 1}`);
        
        if (process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID) {
          await vapi.start(
            undefined, // assistant
            undefined, // assistantOverrides
            undefined, // squad
            process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID, // workflow
            { // workflowOverrides
              variableValues: {
                username: options.userName || 'User',
                userid: options.userId || 'anonymous',
              }
            }
          );
        } else if (process.env.NEXT_PUBLIC_VAPI_ASSISTANT_ID) {
          // Fallback to assistant approach
          const assistantConfig = {
            assistantId: process.env.NEXT_PUBLIC_VAPI_ASSISTANT_ID,
            assistantOverrides: {
              variableValues: {
                username: options.userName || 'User',
                userid: options.userId || 'anonymous',
              }
            }
          };
          await vapi.start(assistantConfig);
        } else {
          throw new Error('No VAPI Workflow ID or Assistant ID configured');
        }

        console.log('VAPI connection successful');
        return; // Success, exit retry loop
        
      } catch (error) {
        this.retryCount++;
        console.error(`Connection attempt ${this.retryCount} failed:`, error);
        
        // Check if it's a permanent error that shouldn't be retried
        if (error instanceof Error) {
          if (error.message.includes('permission') || 
              error.message.includes('microphone') ||
              error.message.includes('browser') ||
              error.message.includes('HTTPS')) {
            throw error; // Don't retry permission/browser issues
          }
        }
        
        if (this.retryCount <= this.maxRetries) {
          console.log(`Retrying in ${this.retryDelay}ms...`);
          await new Promise(resolve => setTimeout(resolve, this.retryDelay));
        } else {
          throw new Error(`Failed to connect after ${this.maxRetries + 1} attempts: ${error instanceof Error ? error.message : String(error)}`);
        }
      }
    }
  }

  stopCall(): void {
    try {
      if (vapi) {
        vapi.stop();
        console.log('VAPI call stopped successfully');
      }
    } catch (error) {
      console.error('Error stopping VAPI call:', error);
    } finally {
      this.isConnecting = false;
      this.retryCount = 0;
    }
  }
}

// Export a singleton instance
export const vapiConnectionManager = new VapiConnectionManager();
