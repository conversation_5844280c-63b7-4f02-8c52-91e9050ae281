"use client";
import { vapi } from "@/lib/vapi.sdk";
import { useEffect, useState } from "react";

const VapiTest = () => {
    const [status, setStatus] = useState('Not initialized');
    const [logs, setLogs] = useState<string[]>([]);

    const addLog = (message: string) => {
        console.log(message);
        setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
    };

    useEffect(() => {
        addLog('Initializing VAPI...');
        addLog(`Environment check:`);
        addLog(`- Public API Key: ${process.env.NEXT_PUBLIC_VAPI_PUBLIC_KEY ? `Set ✓ (${process.env.NEXT_PUBLIC_VAPI_PUBLIC_KEY.substring(0, 8)}...)` : 'Missing ✗'}`);
        addLog(`- Workflow ID: ${process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID ? `Set ✓ (${process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID.substring(0, 8)}...)` : 'Missing ✗'}`);
        addLog(`- Legacy Assistant ID: ${process.env.NEXT_PUBLIC_VAPI_ASSISTANT_ID ? `Set ✓ (${process.env.NEXT_PUBLIC_VAPI_ASSISTANT_ID.substring(0, 8)}...)` : 'Missing ✗'}`);
        addLog(`- Legacy API Token: ${process.env.NEXT_PUBLIC_VAPI_API_TOKEN ? `Set ✓ (${process.env.NEXT_PUBLIC_VAPI_API_TOKEN.substring(0, 8)}...)` : 'Missing ✗'}`);

        // Check if VAPI is properly initialized
        if (vapi) {
            addLog('VAPI instance created successfully ✓');
            setStatus('Initialized');
        } else {
            addLog('Failed to create VAPI instance ✗');
            setStatus('Failed');
            return;
        }

        // Set up event listeners
        const onCallStart = () => {
            addLog('Call started');
            setStatus('Call Active');
        };

        const onCallEnd = () => {
            addLog('Call ended');
            setStatus('Call Ended');
        };

        const onError = (error: Error) => {
            addLog(`Error: ${error.message}`);
            addLog(`Error details: ${JSON.stringify(error)}`);
            setStatus('Error');
        };

        const onMessage = (message: unknown) => {
            addLog(`Message received: ${JSON.stringify(message)}`);
        };

        vapi.on('call-start', onCallStart);
        vapi.on('call-end', onCallEnd);
        vapi.on('error', onError);
        vapi.on('message', onMessage);

        return () => {
            vapi.off('call-start', onCallStart);
            vapi.off('call-end', onCallEnd);
            vapi.off('error', onError);
            vapi.off('message', onMessage);
        };
    }, []);

    const testAssistantIdCall = async () => {
        try {
            addLog('Attempting to start call with Assistant ID (legacy approach)...');
            addLog(`Assistant ID: ${process.env.NEXT_PUBLIC_VAPI_ASSISTANT_ID}`);
            addLog(`Public API Key: ${process.env.NEXT_PUBLIC_VAPI_PUBLIC_KEY ? 'Set' : 'Not set'}`);

            if (!process.env.NEXT_PUBLIC_VAPI_ASSISTANT_ID) {
                addLog('ERROR: Assistant ID not found');
                return;
            }

            await vapi.start(process.env.NEXT_PUBLIC_VAPI_ASSISTANT_ID);
            addLog('✓ Assistant ID call start request sent successfully');
        } catch (error) {
            addLog(`✗ Assistant ID call failed: ${error instanceof Error ? error.message : String(error)}`);
            if (error instanceof Error && error.stack) {
                addLog(`Stack trace: ${error.stack}`);
            }
        }
    };

    const testWorkflowCall = async () => {
        try {
            addLog('Attempting to start call with Workflow ID (primary approach)...');
            addLog(`Workflow ID: ${process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID}`);
            addLog(`Public API Key: ${process.env.NEXT_PUBLIC_VAPI_PUBLIC_KEY ? 'Set' : 'Not set'}`);

            if (!process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID) {
                addLog('ERROR: Workflow ID not found');
                return;
            }

            if (!process.env.NEXT_PUBLIC_VAPI_PUBLIC_KEY) {
                addLog('ERROR: Public API Key not found');
                return;
            }

            addLog('Starting workflow with variable values...');
            await vapi.start(
                undefined, // assistant
                undefined, // assistantOverrides
                undefined, // squad
                process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID, // workflow
                { // workflowOverrides
                    variableValues: {
                        username: 'Test User',
                        userid: 'test123',
                    }
                }
            );

            addLog('✓ Workflow call start request sent successfully');
        } catch (error) {
            addLog(`✗ Workflow call failed: ${error instanceof Error ? error.message : String(error)}`);
            if (error instanceof Error && error.stack) {
                addLog(`Stack trace: ${error.stack}`);
            }

            // Enhanced error logging for API errors
            if (error && typeof error === 'object' && 'response' in error) {
                const errorWithResponse = error as { response?: { status?: number; statusText?: string; text?: () => Promise<string> } };
                addLog(`HTTP Status: ${errorWithResponse.response?.status}`);
                addLog(`Status Text: ${errorWithResponse.response?.statusText}`);
                try {
                    const responseText = await errorWithResponse.response?.text?.();
                    addLog(`Response Body: ${responseText}`);
                } catch {
                    addLog('Could not read response body');
                }
            }
        }
    };

    const testMinimalWorkflow = async () => {
        try {
            addLog('Testing minimal workflow call (no variables)...');

            if (!process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID) {
                addLog('ERROR: Workflow ID not found');
                return;
            }

            // Try with minimal payload - no variable values
            await vapi.start(
                undefined, // assistant
                undefined, // assistantOverrides
                undefined, // squad
                process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID // workflow
            );
            addLog('✓ Minimal workflow call succeeded');
        } catch (error) {
            addLog(`✗ Minimal workflow call failed: ${error instanceof Error ? error.message : String(error)}`);

            // Enhanced error logging
            if (error && typeof error === 'object' && 'response' in error) {
                const errorWithResponse = error as { response?: { status?: number; statusText?: string; text?: () => Promise<string> } };
                addLog(`HTTP Status: ${errorWithResponse.response?.status}`);
                addLog(`Status Text: ${errorWithResponse.response?.statusText}`);
                try {
                    const responseText = await errorWithResponse.response?.text?.();
                    addLog(`Response Body: ${responseText}`);
                } catch {
                    addLog('Could not read response body');
                }
            }
        }
    };

    const testAssistantCall = async () => {
        try {
            addLog('Attempting to start call with assistant configuration...');

            // Create a simple assistant configuration for testing
            const assistantConfig = {
                name: "Test Interviewer",
                firstMessage: "Hello! This is a test call. Can you hear me?",
                transcriber: {
                    provider: "deepgram" as const,
                    model: "nova-2" as const,
                    language: "en" as const,
                },
                voice: {
                    provider: "11labs" as const,
                    voiceId: "sarah" as const,
                },
                model: {
                    provider: "openai" as const,
                    model: "gpt-3.5-turbo" as const,
                    messages: [
                        {
                            role: "system" as const,
                            content: "You are a test assistant. Keep responses brief and friendly."
                        }
                    ]
                }
            };

            await vapi.start(assistantConfig);
            addLog('Assistant call start request sent successfully');
        } catch (error) {
            addLog(`Assistant call failed: ${error instanceof Error ? error.message : String(error)}`);
            if (error instanceof Error && error.stack) {
                addLog(`Stack trace: ${error.stack}`);
            }
        }
    };

    const stopCall = () => {
        try {
            addLog('Stopping call...');
            vapi.stop();
        } catch (error) {
            addLog(`Stop failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    };

    const testMicrophone = async () => {
        try {
            addLog('Testing microphone access...');
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            addLog('✓ Microphone access granted');

            // Stop the stream
            stream.getTracks().forEach(track => track.stop());
            addLog('✓ Microphone test completed');
        } catch (error) {
            addLog(`✗ Microphone access failed: ${error instanceof Error ? error.message : String(error)}`);
            if (error instanceof Error && error.name === 'NotAllowedError') {
                addLog('Please allow microphone access in your browser settings');
            }
        }
    };

    const testConnection = () => {
        addLog('Testing VAPI connection...');
        addLog(`VAPI object methods: ${Object.getOwnPropertyNames(Object.getPrototypeOf(vapi)).join(', ')}`);
        addLog(`VAPI instance type: ${typeof vapi}`);

        // Check browser capabilities
        addLog('Checking browser capabilities...');
        addLog(`- getUserMedia: ${navigator.mediaDevices && typeof navigator.mediaDevices.getUserMedia === 'function' ? '✓' : '✗'}`);
        addLog(`- WebRTC: ${window.RTCPeerConnection ? '✓' : '✗'}`);
        addLog(`- WebAudio: ${window.AudioContext || (window as unknown as { webkitAudioContext?: typeof AudioContext }).webkitAudioContext ? '✓' : '✗'}`);
        addLog(`- HTTPS: ${location.protocol === 'https:' ? '✓' : '✗ (required for microphone access)'}`);

        // Test if we can access VAPI properties
        try {
            addLog('Checking VAPI readiness...');
            // Most VAPI instances have some basic properties we can check
            if (vapi && typeof vapi.start === 'function') {
                addLog('✓ VAPI start method available');
            } else {
                addLog('✗ VAPI start method not available');
            }

            if (vapi && typeof vapi.stop === 'function') {
                addLog('✓ VAPI stop method available');
            } else {
                addLog('✗ VAPI stop method not available');
            }

            if (vapi && typeof vapi.on === 'function') {
                addLog('✓ VAPI event listener method available');
            } else {
                addLog('✗ VAPI event listener method not available');
            }
        } catch (error) {
            addLog(`Connection test failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    };

    return (
        <div className="p-6 max-w-2xl mx-auto">
            <h2 className="text-2xl font-bold mb-4">VAPI Test Component</h2>
            
            <div className="mb-4">
                <p><strong>Status:</strong> {status}</p>
            </div>

            <div className="flex gap-2 mb-6 flex-wrap">
                <button
                    onClick={testConnection}
                    className="px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600 text-sm"
                >
                    Test Connection
                </button>
                <button
                    onClick={testMicrophone}
                    className="px-3 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 text-sm"
                >
                    Test Microphone
                </button>
                <button
                    onClick={testWorkflowCall}
                    className="px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm font-semibold"
                >
                    🚀 Test Workflow (Primary)
                </button>
                <button
                    onClick={testMinimalWorkflow}
                    className="px-3 py-2 bg-cyan-500 text-white rounded hover:bg-cyan-600 text-sm"
                >
                    Test Minimal Workflow
                </button>
                <button
                    onClick={testAssistantIdCall}
                    className="px-3 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600 text-sm"
                >
                    Test Assistant ID (Legacy)
                </button>
                <button
                    onClick={testAssistantCall}
                    className="px-3 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 text-sm"
                >
                    Test Inline Config
                </button>
                <button
                    onClick={stopCall}
                    className="px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600 text-sm"
                >
                    Stop Call
                </button>
            </div>

            <div className="bg-gray-100 p-4 rounded max-h-96 overflow-y-auto">
                <h3 className="font-bold mb-2">Logs:</h3>
                {logs.map((log, index) => (
                    <div key={index} className="text-sm font-mono mb-1">
                        {log}
                    </div>
                ))}
            </div>
        </div>
    );
};

export default VapiTest;
